#!/usr/bin/env python3
"""
Script de test pour vérifier que creation_masque_vide.py est bien intégré
dans l'interface graphique
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Ajouter le répertoire courant au path pour importer interface_graphique
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from interface_graphique import ScriptGUI
except ImportError as e:
    print(f"Erreur d'importation: {e}")
    sys.exit(1)

def test_creation_masque_vide_integration():
    """Teste que creation_masque_vide.py est bien intégré dans l'interface"""
    print("🧪 Test d'intégration de creation_masque_vide.py")
    print("=" * 60)
    
    # Créer une fenêtre de test
    root = tk.Tk()
    root.withdraw()  # Cacher la fenêtre principale
    
    try:
        # Créer l'interface
        app = ScriptGUI(root)
        
        # Vérifier que le script est dans la liste des utilitaires
        utilitaire_scripts = app.scripts_by_category.get("Utilitaire", [])
        
        if "creation_masque_vide.py" in utilitaire_scripts:
            print("✅ creation_masque_vide.py trouvé dans la catégorie Utilitaire")
            
            # Simuler la sélection de la catégorie "Utilitaire"
            app.category_var.set("Utilitaire")
            app.on_category_change()
            
            # Vérifier que le script est disponible dans le combobox
            available_scripts = list(app.script_combo['values'])
            if "creation_masque_vide.py" in available_scripts:
                print("✅ creation_masque_vide.py disponible dans le combobox")
                
                # Simuler la sélection du script
                app.script_var.set("creation_masque_vide.py")
                app.on_script_change()
                
                # Vérifier que les variables ont été créées
                required_vars = [
                    'creation_masque_images_folder',
                    'creation_masque_masks_folder', 
                    'creation_masque_output_folder'
                ]
                
                all_vars_exist = True
                for var_name in required_vars:
                    if hasattr(app, var_name):
                        print(f"  ✅ Variable {var_name}: OK")
                    else:
                        print(f"  ❌ Variable {var_name}: MANQUANTE")
                        all_vars_exist = False
                
                if all_vars_exist:
                    print("✅ Toutes les variables d'interface ont été créées")
                    
                    # Vérifier que les widgets ont été créés dans params_frame
                    widget_count = len(app.params_frame.winfo_children())
                    if widget_count > 0:
                        print(f"✅ Interface créée avec {widget_count} widgets")
                        
                        # Tester la fonction d'exécution (sans vraiment exécuter)
                        if hasattr(app, '_run_creation_masque_vide'):
                            print("✅ Fonction d'exécution _run_creation_masque_vide trouvée")
                        else:
                            print("❌ Fonction d'exécution _run_creation_masque_vide manquante")
                    else:
                        print("❌ Aucun widget créé dans l'interface")
                else:
                    print("❌ Certaines variables d'interface sont manquantes")
            else:
                print("❌ creation_masque_vide.py non disponible dans le combobox")
                print(f"Scripts disponibles: {available_scripts}")
        else:
            print("❌ creation_masque_vide.py non trouvé dans la catégorie Utilitaire")
            print(f"Scripts utilitaires: {utilitaire_scripts}")
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        root.destroy()
    
    print("\n🏁 Test terminé")

if __name__ == "__main__":
    test_creation_masque_vide_integration()
